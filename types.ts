export type OfferCategory = "Education" | "Software & Tools" | "Technology" | "Shopping" | "Entertainment" | "Health & Wellbeing";

export interface OfferSource {
  title: string;
  uri: string;
}

export interface Offer {
  brand: string;
  title: string;
  description: string;
  category: OfferCategory;
  couponCode: string | null;
  offerUrl: string;
  imageUrl: string | null;
  source?: OfferSource | null;
}