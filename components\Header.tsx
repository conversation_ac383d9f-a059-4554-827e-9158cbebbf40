import React from 'react';
import { GraduationCapIcon } from './icons';
import ThemeToggle from './ThemeToggle';

interface HeaderProps {
    theme: 'light' | 'dark';
    onThemeToggle: () => void;
}

export default function Header({ theme, onThemeToggle }: HeaderProps) {
    return (
        <header className="sticky top-0 bg-white/80 dark:bg-slate-900/70 backdrop-blur-lg border-b border-slate-200 dark:border-slate-800 z-10">
            <div className="container mx-auto px-4">
                <div className="flex items-center justify-between h-16">
                    <a href="/" className="flex items-center gap-3 group" aria-label="Student Perks Home">
                        <div className="text-teal-500 dark:text-teal-400 group-hover:drop-shadow-[0_0_8px_rgba(45,212,191,0.5)]">
                           <GraduationCapIcon className="h-7 w-7"/>
                        </div>
                        <span className="text-2xl font-bold text-slate-800 dark:text-slate-100 group-hover:text-slate-900 dark:group-hover:text-white">Student Perks</span>
                    </a>
                    <ThemeToggle theme={theme} onToggle={onThemeToggle} />
                </div>
            </div>
        </header>
    );
}