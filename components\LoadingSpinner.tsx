import React from 'react';

interface LoadingSpinnerProps {
    onCancel: () => void;
}

export default function LoadingSpinner({ onCancel }: LoadingSpinnerProps) {
    return (
        <div className="flex items-center gap-3 bg-slate-100/80 dark:bg-slate-800/80 backdrop-blur-xl border border-slate-300 dark:border-slate-700 rounded-full px-3 py-1">
            <div
                className="animate-spin rounded-full h-5 w-5 border-b-2 border-teal-500 dark:border-teal-400"
                role="status"
                aria-live="polite"
                aria-label="Loading"
            ></div>
            <span className="text-sm text-slate-600 dark:text-slate-300">Unleashing the AI...</span>
             <button onClick={onCancel} className="text-sm text-slate-500 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white hover:bg-red-500/20 dark:hover:bg-red-600/70 rounded-full px-2 py-0.5">Cancel</button>
        </div>
    );
}