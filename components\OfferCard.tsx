import React, { useState, useEffect } from 'react';
import { Offer } from '../types';
import { CopyIcon, CheckIcon, ExternalLinkIcon, BookmarkIcon } from './icons';
import { createPlaceholderImage } from '../utils';

interface OfferCardProps {
    offer: Offer;
    isSaved?: boolean;
    onToggleSave?: () => void;
}

export default function OfferCard({ offer, isSaved, onToggleSave }: OfferCardProps) {
    const [isCopied, setIsCopied] = useState(false);
    // We get the theme from the CSS class on the root element
    const [theme, setTheme] = useState<'light' | 'dark'>('light');

    useEffect(() => {
        const isDark = document.documentElement.classList.contains('dark');
        setTheme(isDark ? 'dark' : 'light');
    }, []);

    const [imageSrc, setImageSrc] = useState(offer.imageUrl || createPlaceholderImage(offer.brand, theme));

    useEffect(() => {
        const isDark = document.documentElement.classList.contains('dark');
        setTheme(isDark ? 'dark' : 'light');
        setImageSrc(offer.imageUrl || createPlaceholderImage(offer.brand, isDark ? 'dark' : 'light'));
    }, [offer.imageUrl, offer.brand]);

    const handleCopyCode = () => {
        if (!offer.couponCode) return;
        navigator.clipboard.writeText(offer.couponCode).then(() => {
            setIsCopied(true);
            setTimeout(() => setIsCopied(false), 2000);
        });
    };
    
    const handleSaveClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        onToggleSave?.();
    }

    return (
        <div className="bg-white dark:bg-slate-800 rounded-lg overflow-hidden flex flex-col group shadow-lg hover:shadow-teal-500/10 border border-slate-200 dark:border-transparent">
            <div className="relative aspect-video overflow-hidden">
                <img
                    src={imageSrc}
                    alt={`${offer.brand} offer`}
                    className="w-full h-full object-cover group-hover:scale-105"
                    loading="lazy"
                    onError={() => {
                        const isDark = document.documentElement.classList.contains('dark');
                        setImageSrc(createPlaceholderImage(offer.brand, isDark ? 'dark' : 'light'));
                    }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                {onToggleSave && (
                    <button 
                        onClick={handleSaveClick}
                        className={`absolute top-3 right-3 z-10 p-2 rounded-full transition-colors duration-200 ${isSaved ? 'bg-teal-500/20 text-teal-400' : 'bg-black/40 backdrop-blur-sm text-slate-300 hover:bg-slate-700/70 hover:text-white'}`}
                        aria-label={isSaved ? 'Unsave this offer' : 'Save this offer'}
                    >
                        <BookmarkIcon isFilled={isSaved} className="w-5 h-5"/>
                    </button>
                )}
                <div className="absolute bottom-0 left-0 p-4">
                    <p className="text-sm font-semibold text-teal-300">{offer.brand}</p>
                    <h3 className="text-xl font-bold font-display text-white mt-1">{offer.title}</h3>
                </div>
            </div>

            <div className="p-5 flex flex-col flex-grow">
                <p className="font-body text-slate-600 dark:text-slate-300 text-sm flex-grow mb-4">{offer.description}</p>
                
                <div className="mt-auto pt-4 flex flex-col gap-3">
                    {offer.couponCode ? (
                        <div className="p-4 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg flex items-center justify-between gap-3">
                            <span className="font-mono text-teal-600 dark:text-teal-400 tracking-wider text-lg">{offer.couponCode}</span>
                             <button
                                onClick={handleCopyCode}
                                className="flex items-center gap-2 px-3 py-1.5 bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 rounded-md hover:bg-teal-500 hover:text-white"
                                aria-label="Copy coupon code"
                            >
                                {isCopied ? <CheckIcon className="text-green-500 dark:text-green-400" /> : <CopyIcon />}
                                <span className={`text-sm font-semibold ${isCopied ? 'text-green-600 dark:text-green-400' : ''}`}>{isCopied ? 'Copied' : 'Copy'}</span>
                            </button>
                        </div>
                    ) : (
                        <a
                            href={offer.offerUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="w-full flex items-center justify-center gap-2 text-center px-4 py-2.5 bg-teal-600 text-white font-semibold rounded-lg hover:bg-teal-500 active:scale-95"
                        >
                            Get Deal
                            <ExternalLinkIcon />
                        </a>
                    )}
                </div>
            </div>
        </div>
    );
}