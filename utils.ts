export function isHttpUrl(str: string): boolean {
  if (!str) return false;
  try {
    const url = new URL(str);
    return url.protocol === 'http:' || url.protocol === 'https:';
  } catch (_) {
    return false;
  }
}

export function createPlaceholderImage(brandName: string, theme: 'light' | 'dark' = 'light'): string {
    const isDark = theme === 'dark';
    const bgColor1 = isDark ? '#1e293b' : '#f1f5f9';
    const bgColor2 = isDark ? '#0f172a' : '#e2e8f0';
    const textColor = isDark ? '#2dd4bf' : '#0d9488';
    
    const svg = `
<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 400 225">
    <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:${bgColor1};stop-opacity:1" />
            <stop offset="100%" style="stop-color:${bgColor2};stop-opacity:1" />
        </linearGradient>
    </defs>
    <rect fill="url(#grad)" width="100%" height="100%"/>
    <text
        x="50%"
        y="50%"
        dominant-baseline="middle"
        text-anchor="middle"
        font-family="Inter, sans-serif"
        font-size="28"
        fill="${textColor}"
        font-weight="bold"
    >
        ${brandName}
    </text>
</svg>
    `.trim();
    return `data:image/svg+xml;base64,${btoa(svg)}`;
}