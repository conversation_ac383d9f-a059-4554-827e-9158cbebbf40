import React from 'react';

const iconProps = {
    "aria-hidden": true,
    xmlns: "http://www.w3.org/2000/svg",
    fill: "none",
    viewBox: "0 0 24 24",
    strokeWidth: "1.5",
    stroke: "currentColor"
};

export const GraduationCapIcon = ({ className = "w-6 h-6" }: { className?: string }) => (
    <svg {...iconProps} className={className} viewBox="0 0 24 24" strokeWidth="1.5">
        <path strokeLinecap="round" strokeLinejoin="round" d="M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.627 48.627 0 0 1 12 20.904a48.627 48.627 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.57 50.57 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0a50.697 50.697 0 0 1-2.658-.813m2.658.814a60.658 60.658 0 0 1 15.482 0m-15.482 0a50.697 50.697 0 0 0-2.658-.813m2.658.814a60.658 60.658 0 0 1 15.482 0" />
    </svg>
);

export const SearchIcon = ({ className = "w-5 h-5" }: { className?: string }) => (
    <svg {...iconProps} className={className} viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
    </svg>
);

export const CopyIcon = ({ className = "w-4 h-4" }: { className?: string }) => (
    <svg {...iconProps} className={className} viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 17.25v3.375c0 .621-.504 1.125-1.125 1.125h-9.75a1.125 1.125 0 0 1-1.125-1.125V7.875c0-.621.504-1.125 1.125-1.125H6.75a9.06 9.06 0 0 1 1.5.124m7.5 10.376h3.375c.621 0 1.125-.504 1.125-1.125V11.25c0-4.46-3.243-8.161-7.5-8.876a9.06 9.06 0 0 0-1.5-.124H9.375c-.621 0-1.125.504-1.125 1.125T8.25 4.5h3.75a7.5 7.5 0 0 1 7.5 7.5v1.5c0 .621-.504 1.125-1.125 1.125Z" />
    </svg>
);

export const CheckIcon = ({ className = "w-4 h-4" }: { className?: string }) => (
    <svg {...iconProps} className={className} viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" d="m4.5 12.75 6 6 9-13.5" />
    </svg>
);

export const ExternalLinkIcon = ({ className = "w-4 h-4" }: { className?: string }) => (
    <svg {...iconProps} className={className} viewBox="0 0 24 24">
      <path strokeLinecap="round" strokeLinejoin="round" d="M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25" />
    </svg>
);

export const XMarkIcon = ({ className = "w-5 h-5" }: { className?: string }) => (
  <svg {...iconProps} className={className} viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" d="M6 18 18 6M6 6l12 12" />
  </svg>
);

export const TagIcon = ({ className = "w-4 h-4" }: { className?: string }) => (
    <svg {...iconProps} className={className}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z" />
        <path strokeLinecap="round" strokeLinejoin="round" d="M6 6h.008v.008H6V6Z" />
    </svg>
);

export const BookmarkIcon = ({ className = "w-5 h-5", isFilled = false }: { className?: string, isFilled?: boolean }) => (
    <svg {...iconProps} className={className} fill={isFilled ? "currentColor" : "none"} viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" d="M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z" />
    </svg>
);

export const SunIcon = ({ className = "w-6 h-6" }: { className?: string }) => (
  <svg {...iconProps} className={className} viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-6.364-.386 1.591-1.591M3 12h2.25m.386-6.364 1.591 1.591M12 12a2.25 2.25 0 0 1-2.25-2.25A2.25 2.25 0 0 1 12 7.5s0 0 0 0a2.25 2.25 0 0 1 2.25 2.25A2.25 2.25 0 0 1 12 12Z" />
  </svg>
);

export const MoonIcon = ({ className = "w-6 h-6" }: { className?: string }) => (
    <svg {...iconProps} className={className} viewBox="0 0 24 24" fill="currentColor">
        <path fillRule="evenodd" d="M9.528 1.718a.75.75 0 0 1 .162.819A8.97 8.97 0 0 0 9 6a9 9 0 0 0 9 9 8.97 8.97 0 0 0 3.463-.69.75.75 0 0 1 .981.981A10.503 10.503 0 0 1 12 22.5a10.5 10.5 0 0 1-10.5-10.5c0-4.368 2.667-8.112 6.46-9.675a.75.75 0 0 1 .818.162Z" clipRule="evenodd" />
    </svg>
);