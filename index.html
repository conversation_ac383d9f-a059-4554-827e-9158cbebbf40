<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Perks</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Bricolage+Grotesque:wght@400;600&family=Inter:wght@400;500;600;700;800&family=Lexend:wght@700;800&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                        display: ['Lexend', 'sans-serif'],
                        body: ['Bricolage Grotesque', 'sans-serif'],
                    },
                    aspectRatio: {
                        'video': '16 / 9',
                    }
                },
            },
        }
    </script>
    <link rel="stylesheet" href="index.css">
<link rel="stylesheet" href="/index.css">
</head>
<body class="bg-white dark:bg-slate-900 text-slate-800 dark:text-slate-200 antialiased">
    <script>
      // Prevents "flash of unstyled content" (FOUC) for the theme
      (function() {
        if (localStorage.theme === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      })();
    </script>
    <div id="root"></div>

    <script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@18.3.1",
    "react-dom/client": "https://esm.sh/react-dom@18.3.1/client",
    "react-dom/": "https://esm.sh/react-dom@18.3.1/",
    "react/": "https://esm.sh/react@18.3.1/"
  }
}
</script>
    <script type="module" src="./index.tsx"></script>
<script type="module" src="/index.tsx"></script>
</body>
</html>