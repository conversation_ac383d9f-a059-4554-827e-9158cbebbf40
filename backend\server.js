const express = require('express');
const cors = require('cors');
const { GoogleGenerativeAI } = require('@google/generative-ai');
require('dotenv').config({ path: '../.env.local' });

const app = express();
const port = 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

app.post('/api/search', async (req, res) => {
    const { query } = req.body;
    
    if (!query) {
        return res.status(400).json({ error: 'Query is required' });
    }

    if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'PLACEHOLDER_API_KEY') {
        return res.status(500).json({ error: 'Gemini API key not configured' });
    }

    try {
        // Set headers for streaming response
        res.setHeader('Content-Type', 'text/plain');
        res.setHeader('Transfer-Encoding', 'chunked');

        const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
        
        const prompt = `Find student discounts and offers related to "${query}". 
        Return ONLY a JSON array of offers, each with these exact fields:
        - brand: string (company name)
        - title: string (offer title)
        - description: string (brief description)
        - category: string (one of: "Education", "Software & Tools", "Technology", "Shopping", "Entertainment", "Health & Wellbeing")
        - couponCode: string or null
        - offerUrl: string (direct link to the offer)
        - imageUrl: string or null (logo/image URL)
        - source: object with title and uri fields

        Focus on real, current student discounts. Return 5-10 relevant offers.
        Format as valid JSON only, no other text.`;

        const result = await model.generateContent(prompt);
        const response = await result.response;
        const text = response.text();

        try {
            // Try to parse as JSON array
            const offers = JSON.parse(text);
            
            if (Array.isArray(offers)) {
                // Stream each offer as a separate JSON line
                for (const offer of offers) {
                    // Add source if not present
                    if (!offer.source) {
                        offer.source = {
                            title: "AI Generated",
                            uri: "gemini-ai"
                        };
                    }
                    
                    res.write(JSON.stringify(offer) + '\n');
                }
            } else {
                // Single offer object
                if (!offers.source) {
                    offers.source = {
                        title: "AI Generated", 
                        uri: "gemini-ai"
                    };
                }
                res.write(JSON.stringify(offers) + '\n');
            }
        } catch (parseError) {
            console.error('Failed to parse Gemini response as JSON:', parseError);
            // Return a fallback offer
            const fallbackOffer = {
                brand: "Search Results",
                title: `Student discounts for ${query}`,
                description: "AI-generated search results. Please verify offers on merchant websites.",
                category: "Education",
                couponCode: null,
                offerUrl: `https://www.google.com/search?q=student+discount+${encodeURIComponent(query)}`,
                imageUrl: null,
                source: {
                    title: "AI Generated",
                    uri: "gemini-ai"
                }
            };
            res.write(JSON.stringify(fallbackOffer) + '\n');
        }

        res.end();
        
    } catch (error) {
        console.error('Error calling Gemini API:', error);
        res.status(500).json({ 
            error: 'Failed to generate offers',
            details: error.message 
        });
    }
});

app.listen(port, () => {
    console.log(`Backend server running at http://localhost:${port}`);
    console.log(`API endpoint: http://localhost:${port}/api/search`);
});
