import React from 'react';
import { SunIcon, MoonIcon } from './icons';

interface ThemeToggleProps {
    theme: 'light' | 'dark';
    onToggle: () => void;
}

export default function ThemeToggle({ theme, onToggle }: ThemeToggleProps) {
    return (
        <button
            onClick={onToggle}
            className="p-2 rounded-full text-slate-500 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-teal-500"
            aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
        >
            {theme === 'light' ? (
                <MoonIcon className="w-6 h-6 text-slate-700" />
            ) : (
                <SunIcon className="w-6 h-6" />
            )}
        </button>
    );
}