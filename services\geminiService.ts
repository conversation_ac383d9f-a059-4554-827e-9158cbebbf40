import { Offer } from '../types';
import { createPlaceholderImage, isHttpUrl } from '../utils';

interface FetchOptions {
  signal?: AbortSignal;
}

export async function* fetchStudentOffers(query: string, options: FetchOptions, theme: 'light' | 'dark'): AsyncGenerator<Offer> {
    const backendUrl = 'http://localhost:3001/api/search'; // This will call your local backend proxy

    try {
        const response = await fetch(backendUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ query }),
            signal: options.signal,
        });

        if (!response.ok) {
            throw new Error(`Backend request failed: ${response.status} ${response.statusText}`);
        }
        if (!response.body) {
            throw new Error('Response body is missing');
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';

        while (true) {
            if (options.signal?.aborted) {
                console.log('Stream fetch aborted by signal.');
                await reader.cancel('Request aborted');
                break;
            }
            
            const { done, value } = await reader.read();
            if (done) break;

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || ''; // Keep any partial line for the next chunk

            for (const line of lines) {
                if (!line.trim().startsWith('{')) continue;
                
                try {
                    const parsed = JSON.parse(line) as Offer;

                    // Basic validation
                    if (!parsed.brand || !parsed.title || !parsed.offerUrl) continue;
                    
                    // The 'source' property is now expected to be added by the backend.
                    
                    if (!parsed.imageUrl || !isHttpUrl(parsed.imageUrl)) {
                        parsed.imageUrl = createPlaceholderImage(parsed.brand, theme);
                    }
                    
                    yield parsed;
                } catch (e) {
                    console.warn('Failed to parse JSON line from backend:', line, e);
                }
            }
        }
    } catch (e: any) {
        if (e.name === 'AbortError') {
            console.log('Fetch aborted.');
            return;
        }
        console.error('Error fetching from backend service:', e);
        throw new Error('Failed to fetch offers. The backend service might be down.');
    }
}