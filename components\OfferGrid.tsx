import React from 'react';
import { Offer } from '../types';
import OfferCard from './OfferCard';

interface OfferGridProps {
    offers: Offer[];
    savedOfferIds?: Set<string>;
    onToggleSave?: (offer: Offer) => void;
}

const getOfferId = (offer: Offer) => `${offer.brand}-${offer.title}`.toLowerCase();

export default function OfferGrid({ offers, savedOfferIds, onToggleSave }: OfferGridProps) {
    if (offers.length === 0) {
        return null;
    }

    return (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 md:gap-8">
            {offers.map((offer, index) => (
                <OfferCard
                    key={`${getOfferId(offer)}-${index}`}
                    offer={offer}
                    isSaved={savedOfferIds?.has(getOfferId(offer))}
                    onToggleSave={onToggleSave ? () => onToggleSave(offer) : undefined}
                />
            ))}
        </div>
    );
}