import { featuredOffers } from '../data/offers';
import { Offer } from '../types';

export function getAllLocalOffers(): Offer[] {
  return featuredOffers;
}

export function searchLocalOffers(query: string): Offer[] {
  if (!query) return [];
  const lowerCaseQuery = query.toLowerCase();
  return featuredOffers.filter(
    offer =>
      offer.brand.toLowerCase().includes(lowerCaseQuery) ||
      offer.title.toLowerCase().includes(lowerCaseQuery) ||
      offer.description.toLowerCase().includes(lowerCaseQuery) ||
      offer.category.toLowerCase().includes(lowerCaseQuery)
  );
}