import React, { useState } from 'react';
import { SearchIcon, XMarkIcon } from './icons';

interface SearchBarProps {
    onSearch: (query: string) => void;
    isLoading: boolean;
    onClear: () => void;
}

export default function SearchBar({ onSearch, isLoading, onClear }: SearchBarProps) {
    const [query, setQuery] = useState('');

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (query.trim()) {
            onSearch(query.trim());
        }
    };
    
    const handleClear = () => {
        setQuery('');
        onClear();
    };

    return (
        <form onSubmit={handleSubmit} className="relative max-w-2xl mx-auto">
            <input
                type="search"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Search for brands, tech, textbooks and more..."
                className="w-full pl-5 pr-24 py-3 sm:pl-6 sm:pr-32 sm:py-4 bg-slate-100 dark:bg-slate-800 border border-slate-300 dark:border-slate-700 rounded-full text-slate-900 dark:text-white placeholder-slate-500 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                aria-label="Search for student offers"
                disabled={isLoading}
            />
            {query && !isLoading && (
                 <button 
                    type="button" 
                    onClick={handleClear}
                    className="absolute right-16 sm:right-20 top-1/2 -translate-y-1/2 p-2 text-slate-500 dark:text-slate-400 hover:text-slate-800 dark:hover:text-white"
                    aria-label="Clear search"
                >
                    <XMarkIcon className="w-5 h-5 sm:w-6 sm:h-6" />
                </button>
            )}
            <button
                type="submit"
                className="absolute right-1.5 top-1/2 -translate-y-1/2 flex items-center justify-center h-10 w-10 sm:h-12 sm:w-14 bg-teal-600 text-white font-semibold rounded-full hover:bg-teal-500 active:scale-95 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-slate-900 disabled:bg-slate-400 dark:disabled:bg-slate-600 disabled:cursor-not-allowed disabled:scale-100"
                aria-label="Search"
                disabled={isLoading}
            >
                <SearchIcon className="w-5 h-5 sm:w-6 sm:h-6" />
            </button>
        </form>
    );
}