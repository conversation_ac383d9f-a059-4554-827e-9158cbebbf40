import React from 'react';

interface CategoriesProps<T extends string> {
    categories: readonly T[];
    activeCategory: T;
    onSelectCategory: (category: T) => void;
}

export default function Categories<T extends string>({ categories, activeCategory, onSelectCategory }: CategoriesProps<T>) {
    return (
        <div className="flex flex-nowrap overflow-x-auto sm:flex-wrap sm:justify-center gap-3 pb-3 -mb-3 category-scrollbar">
            {categories.map((category) => (
                <button
                    key={category}
                    onClick={() => onSelectCategory(category)}
                    className={`px-4 py-2 text-sm font-semibold rounded-full whitespace-nowrap border ${
                        activeCategory === category
                            ? 'bg-teal-600 text-white border-transparent shadow-lg shadow-teal-500/20'
                            : 'bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-700 text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700 hover:text-slate-900 dark:hover:text-white hover:border-slate-400 dark:hover:border-slate-600'
                    }`}
                    aria-pressed={activeCategory === category}
                >
                    {category}
                </button>
            ))}
        </div>
    );
}