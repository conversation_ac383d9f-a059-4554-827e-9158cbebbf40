import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Offer, OfferCategory } from './types';
import { getAllLocalOffers, searchLocalOffers } from './services/localOfferService';
import { fetchStudentOffers } from './services/geminiService';
import Header from './components/Header';
import SearchBar from './components/SearchBar';
import Categories from './components/Categories';
import OfferGrid from './components/OfferGrid';
import Footer from './components/Footer';
import LoadingSpinner from './components/LoadingSpinner';

const ALL_CATEGORIES = ['All', 'Education', 'Software & Tools', 'Technology', 'Shopping', 'Entertainment', 'Health & Wellbeing', 'Saved'] as const;

type AppCategory = (typeof ALL_CATEGORIES)[number];
type Theme = 'light' | 'dark';

const getOfferId = (offer: Offer) => `${offer.brand}-${offer.title}`.toLowerCase();

export default function App() {
    const [searchQuery, setSearchQuery] = useState('');
    const [featuredOffers, setFeaturedOffers] = useState<Offer[]>([]);
    const [localResults, setLocalResults] = useState<Offer[]>([]);
    const [webResults, setWebResults] = useState<Offer[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [activeCategory, setActiveCategory] = useState<AppCategory>('All');
    const [theme, setTheme] = useState<Theme>(() => (localStorage.getItem('theme') as Theme) || 'light');
    const [savedOfferIds, setSavedOfferIds] = useState<Set<string>>(() => {
        try {
            const saved = localStorage.getItem('savedOffers');
            return saved ? new Set(JSON.parse(saved)) : new Set();
        } catch {
            return new Set();
        }
    });

    const abortControllerRef = useRef<AbortController | null>(null);

    useEffect(() => {
        setFeaturedOffers(getAllLocalOffers());
    }, []);
    
    useEffect(() => {
        if (theme === 'dark') {
            document.documentElement.classList.add('dark');
        } else {
            document.documentElement.classList.remove('dark');
        }
        localStorage.setItem('theme', theme);
    }, [theme]);


    useEffect(() => {
        try {
            localStorage.setItem('savedOffers', JSON.stringify(Array.from(savedOfferIds)));
        } catch (e) {
            console.error("Failed to save offers to localStorage", e);
        }
    }, [savedOfferIds]);

    const handleToggleSave = (offer: Offer) => {
        const offerId = getOfferId(offer);
        setSavedOfferIds(prev => {
            const newSet = new Set(prev);
            if (newSet.has(offerId)) {
                newSet.delete(offerId);
            } else {
                newSet.add(offerId);
            }
            return newSet;
        });
    };

    const handleSearch = async (query: string) => {
        if (abortControllerRef.current) {
            abortControllerRef.current.abort();
        }
        
        setSearchQuery(query);
        setIsLoading(true);
        setError(null);
        setWebResults([]);
        setActiveCategory('All');
        
        if (!query) {
            setLocalResults([]);
            setIsLoading(false);
            return;
        }
        
        const newAbortController = new AbortController();
        abortControllerRef.current = newAbortController;
        
        const localOffers = searchLocalOffers(query);
        setLocalResults(localOffers);
        const localOfferIdentifiers = new Set(localOffers.map(o => getOfferId(o)));

        try {
            for await (const offer of fetchStudentOffers(query, { signal: newAbortController.signal }, theme)) {
                const offerIdentifier = getOfferId(offer);
                if (!localOfferIdentifiers.has(offerIdentifier)) {
                    setWebResults(prev => [...prev, offer]);
                }
            }
        } catch (e: any) {
            if (e.name !== 'AbortError') {
                console.error("Error fetching student offers:", e);
                setError("Sorry, the AI search failed. Please try again later.");
            }
        } finally {
            if (abortControllerRef.current === newAbortController) {
                setIsLoading(false);
                abortControllerRef.current = null;
            }
        }
    };

    const handleCancelSearch = () => {
        if (abortControllerRef.current) {
            abortControllerRef.current.abort();
            setIsLoading(false);
            abortControllerRef.current = null;
        }
    };
    
    const handleClearSearch = () => {
        setSearchQuery('');
        setLocalResults([]);
        setWebResults([]);
        setError(null);
        setActiveCategory('All');
        if (abortControllerRef.current) {
            abortControllerRef.current.abort();
            setIsLoading(false);
            abortControllerRef.current = null;
        }
    }
    
    const allAvailableOffers = useMemo(() => {
        const allOffersMap = new Map<string, Offer>();
        const offersToAdd = [...featuredOffers, ...localResults, ...webResults];
        
        offersToAdd.forEach(offer => {
            allOffersMap.set(getOfferId(offer), offer);
        });
        
        return Array.from(allOffersMap.values());
    }, [featuredOffers, localResults, webResults]);


    const offersToShow = useMemo(() => {
        if (activeCategory === 'Saved') {
            return allAvailableOffers.filter(offer => savedOfferIds.has(getOfferId(offer)));
        }

        const sourceOffers = searchQuery ? [...localResults, ...webResults] : featuredOffers;
        if (activeCategory === 'All') {
            return sourceOffers;
        }
        return sourceOffers.filter(offer => offer.category === activeCategory);
    }, [searchQuery, localResults, webResults, featuredOffers, activeCategory, savedOfferIds, allAvailableOffers]);

    const handleSelectCategory = (category: AppCategory) => {
        setActiveCategory(category);
    };

    const hasSearchResults = localResults.length > 0 || webResults.length > 0;
    
    const filteredLocalResults = useMemo(() => localResults.filter(o => activeCategory === 'All' || o.category === activeCategory), [localResults, activeCategory]);
    const filteredWebResults = useMemo(() => webResults.filter(o => activeCategory === 'All' || o.category === activeCategory), [webResults, activeCategory]);

    const renderContent = () => {
        if (activeCategory === 'Saved') {
            if (offersToShow.length > 0) {
                return (
                    <div className="mt-16">
                        <h2 className="text-3xl font-bold mb-6 text-slate-900 dark:text-white">Your Saved Offers</h2>
                        <OfferGrid offers={offersToShow} onToggleSave={handleToggleSave} savedOfferIds={savedOfferIds} />
                    </div>
                );
            }
            return (
                <div className="text-center py-16">
                    <h3 className="text-2xl font-bold text-slate-800 dark:text-slate-200">No Saved Offers Yet</h3>
                    <p className="text-slate-500 dark:text-slate-400 mt-2 text-base sm:text-lg">Click the bookmark icon on any offer to save it here for later.</p>
                </div>
            );
        }

        if (searchQuery) {
             return (
                <div className="mt-16">
                    {filteredLocalResults.length > 0 && (
                        <section>
                            <h2 className="text-3xl font-bold mb-6 text-slate-900 dark:text-white">Featured Offers</h2>
                            <OfferGrid offers={filteredLocalResults} onToggleSave={handleToggleSave} savedOfferIds={savedOfferIds}/>
                        </section>
                    )}

                    <section className={filteredLocalResults.length > 0 ? "mt-12" : ""}>
                        <div className="flex flex-col items-start gap-4 sm:flex-row sm:items-center sm:justify-between mb-6">
                            <h2 className="text-3xl font-bold text-slate-900 dark:text-white">Discovered by AI</h2>
                            {isLoading && <LoadingSpinner onCancel={handleCancelSearch} />}
                        </div>
                        {filteredWebResults.length > 0 && (
                            <OfferGrid offers={filteredWebResults} onToggleSave={handleToggleSave} savedOfferIds={savedOfferIds}/>
                        )}
                    </section>
                    
                    {!isLoading && !hasSearchResults && (
                        <div className="text-center py-16">
                            <h3 className="text-2xl font-bold text-slate-800 dark:text-slate-200">No Offers Found</h3>
                            <p className="text-slate-500 dark:text-slate-400 mt-2 text-base sm:text-lg">Your search for "{searchQuery}" did not return any results. Try a different keyword.</p>
                        </div>
                    )}
                </div>
            )
        }
        
        return (
            <div className="mt-16">
                 <h2 className="text-3xl font-bold mb-6 text-slate-900 dark:text-white">Featured Offers</h2>
                <OfferGrid offers={offersToShow} onToggleSave={handleToggleSave} savedOfferIds={savedOfferIds}/>
            </div>
        );
    }

    return (
        <div className="flex flex-col min-h-screen">
            <Header theme={theme} onThemeToggle={() => setTheme(t => t === 'light' ? 'dark' : 'light')} />
            <main className="flex-grow container mx-auto px-4 py-8 sm:py-12">
                <div className="max-w-4xl mx-auto text-center">
                    <h1 className="text-4xl sm:text-6xl font-extrabold font-display dark:text-white mb-4 bg-gradient-to-r from-teal-500 to-teal-600 dark:from-teal-400 dark:to-teal-500 text-transparent bg-clip-text">
                        Student Savings, Supercharged
                    </h1>
                    <p className="text-slate-600 dark:text-slate-400 text-base sm:text-lg max-w-2xl mx-auto mb-8">Your AI-powered hub for exclusive student deals. From textbooks to tech, we find the perks so you can focus on the learning.</p>
                    
                    <div className="mb-8">
                       <Categories
                            categories={ALL_CATEGORIES}
                            activeCategory={activeCategory}
                            onSelectCategory={handleSelectCategory}
                        />
                    </div>

                    <SearchBar onSearch={handleSearch} isLoading={isLoading} onClear={handleClearSearch} />
                </div>

                {renderContent()}

                 {error && (
                    <div className="text-center py-16 mt-8 bg-red-100 dark:bg-red-900/20 rounded-lg">
                        <h3 className="text-2xl font-bold text-red-600 dark:text-red-400">Search Error</h3>
                        <p className="text-slate-500 dark:text-slate-400 mt-2 text-base sm:text-lg">{error}</p>
                    </div>
                )}
            </main>
            <Footer />
        </div>
    );
}